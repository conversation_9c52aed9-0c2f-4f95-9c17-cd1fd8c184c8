import pygame

class SciFiTheme:
    def __init__(self):
        # Sci-Fi Color Scheme
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.GRAY = (128, 128, 128)
        self.GREEN = (0, 255, 0)
        self.RED = (255, 0, 0)
        self.BLUE = (0, 100, 255)
        self.TRANSPARENT = (0, 0, 0, 0)
        
        # Sci-Fi themed colors
        self.CYAN = (0, 255, 255)
        self.NEON_BLUE = (0, 150, 255)
        self.NEON_GREEN = (0, 255, 100)
        self.ORANGE = (255, 165, 0)
        self.PURPLE = (128, 0, 255)
        self.DARK_BLUE = (0, 50, 100)
        self.DARK_GRAY = (40, 40, 50)
        self.LIGHT_GRAY = (180, 180, 190)
        
        # Fonts
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        self.title_font = pygame.font.Font(None, 48)
    
    def draw_sci_fi_button(self, surface, rect, text, active=False, button_type="normal"):
        """Draw a sci-fi styled button"""
        if button_type == "arrow_left":
            # Left arrow button
            button_color = self.DARK_GRAY
            border_color = self.NEON_BLUE
            pygame.draw.rect(surface, button_color, rect)
            pygame.draw.rect(surface, border_color, rect, 2)
            # Draw left arrow
            pygame.draw.polygon(surface, self.CYAN, [
                (rect.x + rect.width - 5, rect.y + 5),
                (rect.x + 10, rect.y + rect.height // 2),
                (rect.x + rect.width - 5, rect.y + rect.height - 5)
            ])
        elif button_type == "arrow_right":
            # Right arrow button
            button_color = self.DARK_GRAY
            border_color = self.NEON_BLUE
            pygame.draw.rect(surface, button_color, rect)
            pygame.draw.rect(surface, border_color, rect, 2)
            # Draw right arrow
            pygame.draw.polygon(surface, self.CYAN, [
                (rect.x + 5, rect.y + 5),
                (rect.x + rect.width - 10, rect.y + rect.height // 2),
                (rect.x + 5, rect.y + rect.height - 5)
            ])
        else:
            # Normal button
            button_color = self.DARK_GRAY if not active else self.NEON_BLUE
            pygame.draw.rect(surface, button_color, rect)
            pygame.draw.rect(surface, self.CYAN, rect, 2)
            
            # Add corner highlights
            pygame.draw.rect(surface, self.ORANGE, (rect.x, rect.y, 5, 5))
            pygame.draw.rect(surface, self.ORANGE, (rect.x + rect.width - 5, rect.y, 5, 5))
        
        # Draw text
        if text:
            text_surface = self.small_font.render(text, True, self.WHITE)
            text_rect = text_surface.get_rect(center=rect.center)
            surface.blit(text_surface, text_rect)
    
    def draw_sci_fi_panel(self, surface, rect):
        """Draw a sci-fi themed panel"""
        # Create panel surface
        panel_surface = pygame.Surface((rect.width, rect.height))
        panel_surface.fill(self.DARK_BLUE)
        
        # Draw sci-fi border with multiple layers
        pygame.draw.rect(panel_surface, self.CYAN, (0, 0, rect.width, rect.height), 2)
        pygame.draw.rect(panel_surface, self.NEON_BLUE, (2, 2, rect.width - 4, rect.height - 4), 1)
        
        # Draw corner accents
        corner_size = 10
        for corner in [(0, 0), (rect.width - corner_size, 0), 
                      (0, rect.height - corner_size), 
                      (rect.width - corner_size, rect.height - corner_size)]:
            pygame.draw.rect(panel_surface, self.ORANGE, (corner[0], corner[1], corner_size, corner_size))
        
        # Blit to main surface
        surface.blit(panel_surface, rect.topleft)
    
    def draw_startup_screen(self, surface, input_text, current_monitor, available_monitors, custom_resolution):
        """Draw the sci-fi themed startup screen"""
        # Fill background
        surface.fill(self.DARK_BLUE)
        
        # Draw grid pattern for sci-fi effect
        for x in range(0, surface.get_width(), 20):
            pygame.draw.line(surface, (0, 30, 60), (x, 0), (x, surface.get_height()), 1)
        for y in range(0, surface.get_height(), 20):
            pygame.draw.line(surface, (0, 30, 60), (0, y), (surface.get_width(), y), 1)
        
        # Title with glow effect
        title_text = self.title_font.render("marine.EXE", True, self.CYAN)
        title_rect = title_text.get_rect(center=(300, 50))
        # Draw glow effect
        for offset in [(2, 2), (-2, -2), (2, -2), (-2, 2)]:
            glow_text = self.title_font.render("marine.EXE", True, self.NEON_BLUE)
            surface.blit(glow_text, (title_rect.x + offset[0], title_rect.y + offset[1]))
        surface.blit(title_text, title_rect)

        # Subtitle
        subtitle_text = self.small_font.render("DESKTOP RPG", True, self.ORANGE)
        subtitle_rect = subtitle_text.get_rect(center=(300, 80))
        surface.blit(subtitle_text, subtitle_rect)

        # Input prompt
        prompt_text = self.font.render("ENTER OPERATIVE DESIGNATION:", True, self.NEON_GREEN)
        prompt_rect = prompt_text.get_rect(center=(300, 120))
        surface.blit(prompt_text, prompt_rect)

        # Input box
        input_box = pygame.Rect(150, 150, 300, 32)
        pygame.draw.rect(surface, self.DARK_GRAY, input_box)
        pygame.draw.rect(surface, self.CYAN, input_box, 2)
        pygame.draw.rect(surface, self.NEON_BLUE, (input_box.x - 2, input_box.y - 2, input_box.width + 4, input_box.height + 4), 1)

        # Input text with cursor
        text_surface = self.font.render(input_text, True, self.WHITE)
        surface.blit(text_surface, (input_box.x + 5, input_box.y + 5))
        
        # Blinking cursor
        import time
        if int(time.time() * 2) % 2:
            cursor_x = input_box.x + 5 + text_surface.get_width()
            pygame.draw.line(surface, self.CYAN, (cursor_x, input_box.y + 5), (cursor_x, input_box.y + 25), 2)

        # Display Settings Section
        settings_y = 220
        settings_rect = pygame.Rect(30, settings_y - 10, 540, 120)
        pygame.draw.rect(surface, (0, 40, 80), settings_rect)
        pygame.draw.rect(surface, self.CYAN, settings_rect, 2)
        
        settings_title = self.font.render("DISPLAY CONFIGURATION", True, self.ORANGE)
        settings_title_rect = settings_title.get_rect(center=(300, settings_y))
        surface.blit(settings_title, settings_title_rect)

        # Monitor setting
        monitor_y = settings_y + 40
        monitor = available_monitors[current_monitor]
        monitor_label = self.small_font.render("MONITOR UNIT:", True, self.NEON_GREEN)
        surface.blit(monitor_label, (50, monitor_y))

        # Monitor arrows
        prev_mon_rect = pygame.Rect(150, monitor_y, 35, 25)
        next_mon_rect = pygame.Rect(420, monitor_y, 35, 25)
        self.draw_sci_fi_button(surface, prev_mon_rect, "", False, "arrow_left")
        self.draw_sci_fi_button(surface, next_mon_rect, "", False, "arrow_right")

        monitor_text = self.small_font.render(f"#{current_monitor + 1} {monitor['name'][:15]}", True, self.CYAN)
        monitor_text_rect = monitor_text.get_rect(center=(300, monitor_y + 12))
        surface.blit(monitor_text, monitor_text_rect)

        # Resolution setting
        res_y = monitor_y + 35
        res_label = self.small_font.render("RESOLUTION:", True, self.NEON_GREEN)
        surface.blit(res_label, (50, res_y))

        # Resolution arrows
        prev_res_rect = pygame.Rect(150, res_y, 35, 25)
        next_res_rect = pygame.Rect(420, res_y, 35, 25)
        self.draw_sci_fi_button(surface, prev_res_rect, "", False, "arrow_left")
        self.draw_sci_fi_button(surface, next_res_rect, "", False, "arrow_right")

        if custom_resolution:
            res_text = f"{custom_resolution[0]}x{custom_resolution[1]}"
            res_color = self.NEON_GREEN
        else:
            res_text = f"{monitor['width']}x{monitor['height']}"
            res_color = self.CYAN
        resolution_display = self.small_font.render(res_text, True, res_color)
        resolution_display_rect = resolution_display.get_rect(center=(300, res_y + 12))
        surface.blit(resolution_display, resolution_display_rect)

        # Instructions
        instr_text = self.font.render(">>> PRESS ENTER TO INITIALIZE SYSTEM <<<", True, self.ORANGE)
        instr_rect = instr_text.get_rect(center=(300, 360))
        surface.blit(instr_text, instr_rect)
        
        # Status indicator
        status_text = self.small_font.render("SYSTEM STATUS: READY", True, self.NEON_GREEN)
        status_text_rect = status_text.get_rect(center=(300, 380))
        surface.blit(status_text, status_text_rect)
        
        return {
            'prev_mon_rect': prev_mon_rect,
            'next_mon_rect': next_mon_rect,
            'prev_res_rect': prev_res_rect,
            'next_res_rect': next_res_rect
        }

    def draw_options_panel(self, surface, options_rect, sound_manager, current_monitor, available_monitors,
                          custom_resolution, texture_zoom, editing_width, editing_height,
                          custom_width_input, custom_height_input):
        """Draw the sci-fi themed options panel"""
        # Options content background
        pygame.draw.rect(surface, (20, 30, 50), options_rect)  # Darker blue for options area
        pygame.draw.rect(surface, self.CYAN, options_rect, 2)

        # Add corner accents
        corner_size = 8
        for corner in [(options_rect.x, options_rect.y),
                      (options_rect.x + options_rect.width - corner_size, options_rect.y),
                      (options_rect.x, options_rect.y + options_rect.height - corner_size),
                      (options_rect.x + options_rect.width - corner_size, options_rect.y + options_rect.height - corner_size)]:
            pygame.draw.rect(surface, self.ORANGE, (corner[0], corner[1], corner_size, corner_size))

        # Sound toggle
        sound_status = "ONLINE" if sound_manager.is_sound_enabled() else "OFFLINE"
        sound_color = self.NEON_GREEN if sound_manager.is_sound_enabled() else self.RED
        sound_text = self.small_font.render(f"AUDIO SYSTEM: {sound_status}", True, sound_color)
        surface.blit(sound_text, (options_rect.x + 10, options_rect.y + 10))

        # Volume control with sci-fi buttons
        volume_text = self.small_font.render(f"VOLUME: {int(sound_manager.volume * 100)}%", True, self.WHITE)
        surface.blit(volume_text, (options_rect.x + 10, options_rect.y + 30))

        # Volume buttons
        vol_minus_rect = pygame.Rect(options_rect.x + 150, options_rect.y + 30, 25, 15)
        vol_plus_rect = pygame.Rect(options_rect.x + 180, options_rect.y + 30, 25, 15)
        self.draw_sci_fi_button(surface, vol_minus_rect, "-")
        self.draw_sci_fi_button(surface, vol_plus_rect, "+")

        # Monitor selection
        monitor_label = self.small_font.render("DISPLAY UNIT:", True, self.NEON_GREEN)
        surface.blit(monitor_label, (options_rect.x + 10, options_rect.y + 50))

        current_monitor_info = available_monitors[current_monitor]
        monitor_text = self.small_font.render(f"#{current_monitor + 1} {current_monitor_info['name'][:15]}", True, self.CYAN)
        surface.blit(monitor_text, (options_rect.x + 100, options_rect.y + 50))

        # Monitor change buttons
        prev_mon_rect = pygame.Rect(options_rect.x + 280, options_rect.y + 50, 25, 15)
        next_mon_rect = pygame.Rect(options_rect.x + 310, options_rect.y + 50, 25, 15)
        self.draw_sci_fi_button(surface, prev_mon_rect, "", False, "arrow_left")
        self.draw_sci_fi_button(surface, next_mon_rect, "", False, "arrow_right")

        # Resolution with arrows
        res_label = self.small_font.render("RESOLUTION:", True, self.NEON_GREEN)
        surface.blit(res_label, (options_rect.x + 10, options_rect.y + 75))

        # Resolution arrows
        prev_res_rect = pygame.Rect(options_rect.x + 90, options_rect.y + 75, 25, 15)
        next_res_rect = pygame.Rect(options_rect.x + 280, options_rect.y + 75, 25, 15)
        self.draw_sci_fi_button(surface, prev_res_rect, "", False, "arrow_left")
        self.draw_sci_fi_button(surface, next_res_rect, "", False, "arrow_right")

        if custom_resolution:
            res_text = f"{custom_resolution[0]}x{custom_resolution[1]}"
            res_color = self.NEON_GREEN
        else:
            res_text = f"{current_monitor_info['width']}x{current_monitor_info['height']}"
            res_color = self.CYAN

        resolution_text = self.small_font.render(res_text, True, res_color)
        surface.blit(resolution_text, (options_rect.x + 120, options_rect.y + 75))

        # Custom resolution inputs
        width_label = self.small_font.render("W:", True, self.WHITE)
        surface.blit(width_label, (options_rect.x + 10, options_rect.y + 100))

        width_input_rect = pygame.Rect(options_rect.x + 30, options_rect.y + 100, 60, 15)
        width_color = self.NEON_GREEN if editing_width else self.CYAN
        pygame.draw.rect(surface, self.DARK_GRAY, width_input_rect)
        pygame.draw.rect(surface, width_color, width_input_rect, 1)
        width_text = self.small_font.render(custom_width_input, True, self.WHITE)
        surface.blit(width_text, (width_input_rect.x + 2, width_input_rect.y + 1))

        height_label = self.small_font.render("H:", True, self.WHITE)
        surface.blit(height_label, (options_rect.x + 100, options_rect.y + 100))

        height_input_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 100, 60, 15)
        height_color = self.NEON_GREEN if editing_height else self.CYAN
        pygame.draw.rect(surface, self.DARK_GRAY, height_input_rect)
        pygame.draw.rect(surface, height_color, height_input_rect, 1)
        height_text = self.small_font.render(custom_height_input, True, self.WHITE)
        surface.blit(height_text, (height_input_rect.x + 2, height_input_rect.y + 1))

        # Apply resolution button
        apply_res_rect = pygame.Rect(options_rect.x + 190, options_rect.y + 100, 50, 15)
        self.draw_sci_fi_button(surface, apply_res_rect, "APPLY")

        # Zoom controls
        zoom_label = self.small_font.render("ZOOM LEVEL:", True, self.NEON_GREEN)
        surface.blit(zoom_label, (options_rect.x + 10, options_rect.y + 125))

        # Zoom buttons
        zoom_minus_rect = pygame.Rect(options_rect.x + 80, options_rect.y + 125, 25, 15)
        zoom_plus_rect = pygame.Rect(options_rect.x + 150, options_rect.y + 125, 25, 15)
        self.draw_sci_fi_button(surface, zoom_minus_rect, "-")
        self.draw_sci_fi_button(surface, zoom_plus_rect, "+")

        zoom_text = f"{int(texture_zoom * 100)}%"
        zoom_display = self.small_font.render(zoom_text, True, self.CYAN)
        surface.blit(zoom_display, (options_rect.x + 110, options_rect.y + 125))

        # Save/Restart button
        save_btn_rect = pygame.Rect(options_rect.x + 10, options_rect.y + 150, 120, 25)
        self.draw_sci_fi_button(surface, save_btn_rect, "SAVE & RESTART")

        # Close Config button
        close_btn_rect = pygame.Rect(options_rect.x + 140, options_rect.y + 150, 100, 25)
        self.draw_sci_fi_button(surface, close_btn_rect, "CLOSE CONFIG")

        return {
            'vol_minus_rect': vol_minus_rect,
            'vol_plus_rect': vol_plus_rect,
            'prev_mon_rect': prev_mon_rect,
            'next_mon_rect': next_mon_rect,
            'prev_res_rect': prev_res_rect,
            'next_res_rect': next_res_rect,
            'width_input_rect': width_input_rect,
            'height_input_rect': height_input_rect,
            'apply_res_rect': apply_res_rect,
            'zoom_minus_rect': zoom_minus_rect,
            'zoom_plus_rect': zoom_plus_rect,
            'save_btn_rect': save_btn_rect,
            'close_btn_rect': close_btn_rect
        }
