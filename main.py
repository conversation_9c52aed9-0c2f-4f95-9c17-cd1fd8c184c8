import pygame
import sys
import os
import ctypes
from ctypes import wintypes
from utility.game_state import GameS<PERSON>
from utility.player import Player
from utility.monster import <PERSON><PERSON>anager
from utility.map_loader import MapLoader
from utility.image_handler import ImageHandler
from utility.damage_display import DamageDisplayManager
from utility.sound_manager import SoundManager
from utility.ui_theme import SciFiTheme

class MarineEXE:
    def __init__(self):
        pygame.init()
        pygame.mixer.init()  # Initialize mixer for sound

        # Initialize display settings first
        self.current_monitor = 0
        self.custom_resolution = None

        # Manual DPI scaling
        self.manual_dpi_scaling = None
        self.dpi_input = ""
        self.editing_dpi = False

        # Custom resolution input
        self.custom_width_input = ""
        self.custom_height_input = ""
        self.editing_width = False
        self.editing_height = False

        # Cache the scale factor to avoid repeated detection
        self.cached_scale_factor = None

        # Zoom functionality
        self.texture_zoom = 1.0  # Default zoom level
        self.min_zoom = 0.5
        self.max_zoom = 3.0
        self.zoom_step = 0.25

        # Now get available monitors (after manual_dpi_scaling is initialized)
        self.available_monitors = self.get_available_monitors()

        # Get desktop size with proper DPI handling
        self.setup_display()

        print(f"Using resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")
        print(f"Available monitors: {len(self.available_monitors)}")

        # Create fullscreen window
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Make window transparent and overlay on desktop
        self.setup_transparent_window()
        
        # Game clock
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Game components
        self.game_state = GameState()
        self.image_handler = ImageHandler()
        self.image_handler.set_texture_zoom(self.texture_zoom)  # Initialize zoom
        self.map_loader = MapLoader()
        self.sound_manager = SoundManager()
        self.ui_theme = SciFiTheme()  # Initialize UI theme

        # Game objects
        self.player = None
        self.monster_manager = None
        self.damage_display = DamageDisplayManager()

        # Legacy color references (for compatibility)
        self.BLACK = self.ui_theme.BLACK
        self.WHITE = self.ui_theme.WHITE
        self.GRAY = self.ui_theme.GRAY
        self.GREEN = self.ui_theme.GREEN
        self.RED = self.ui_theme.RED
        self.BLUE = self.ui_theme.BLUE
        self.CYAN = self.ui_theme.CYAN
        self.NEON_BLUE = self.ui_theme.NEON_BLUE
        self.NEON_GREEN = self.ui_theme.NEON_GREEN
        self.ORANGE = self.ui_theme.ORANGE
        self.DARK_BLUE = self.ui_theme.DARK_BLUE
        self.DARK_GRAY = self.ui_theme.DARK_GRAY

        # UI (use theme fonts)
        self.font = self.ui_theme.font
        self.small_font = self.ui_theme.small_font
        self.info_panel_rect = pygame.Rect(10, 10, 400, 180)  # Made taller for options button
        self.panel_dragging = False
        self.panel_drag_offset_x = 0
        self.panel_drag_offset_y = 0

        # Options button (relative to panel)
        self.options_button_rect = pygame.Rect(320, 150, 70, 25)
        self.show_options = False
        self.options_button_rects = {}  # Store button rectangles for options panel

    def get_windows_scale_factor(self):
        """Get Windows Scale and Layout setting"""
        # Use manual scaling if set
        if self.manual_dpi_scaling:
            return self.manual_dpi_scaling

        # Return cached value if available
        if self.cached_scale_factor:
            return self.cached_scale_factor

        try:
            user32 = ctypes.windll.user32

            # Method 1: Get actual vs scaled resolution
            try:
                # Get what Windows reports (scaled)
                scaled_width = user32.GetSystemMetrics(0)
                scaled_height = user32.GetSystemMetrics(1)

                # Get actual physical resolution
                class DEVMODE(ctypes.Structure):
                    _fields_ = [
                        ('dmDeviceName', ctypes.c_wchar * 32),
                        ('dmSpecVersion', wintypes.WORD),
                        ('dmDriverVersion', wintypes.WORD),
                        ('dmSize', wintypes.WORD),
                        ('dmDriverExtra', wintypes.WORD),
                        ('dmFields', wintypes.DWORD),
                        ('dmOrientation', ctypes.c_short),
                        ('dmPaperSize', ctypes.c_short),
                        ('dmPaperLength', ctypes.c_short),
                        ('dmPaperWidth', ctypes.c_short),
                        ('dmScale', ctypes.c_short),
                        ('dmCopies', ctypes.c_short),
                        ('dmDefaultSource', ctypes.c_short),
                        ('dmPrintQuality', ctypes.c_short),
                        ('dmColor', ctypes.c_short),
                        ('dmDuplex', ctypes.c_short),
                        ('dmYResolution', ctypes.c_short),
                        ('dmTTOption', ctypes.c_short),
                        ('dmCollate', ctypes.c_short),
                        ('dmFormName', ctypes.c_wchar * 32),
                        ('dmLogPixels', wintypes.WORD),
                        ('dmBitsPerPel', wintypes.DWORD),
                        ('dmPelsWidth', wintypes.DWORD),
                        ('dmPelsHeight', wintypes.DWORD),
                        ('dmDisplayFlags', wintypes.DWORD),
                        ('dmDisplayFrequency', wintypes.DWORD),
                    ]

                devmode = DEVMODE()
                devmode.dmSize = ctypes.sizeof(DEVMODE)

                if user32.EnumDisplaySettingsW(None, -1, ctypes.byref(devmode)):
                    actual_width = devmode.dmPelsWidth
                    actual_height = devmode.dmPelsHeight

                    scale_factor = actual_width / scaled_width
                    scale_percent = int(scale_factor * 100)

                    print(f"Windows Scale and Layout detected: {scale_percent}%")
                    print(f"Physical resolution: {actual_width}x{actual_height}")
                    print(f"Scaled resolution: {scaled_width}x{scaled_height}")

                    # Cache the result
                    self.cached_scale_factor = scale_factor
                    return scale_factor
            except Exception as e:
                print(f"Scale detection failed: {e}")

            # Fallback: assume 125% scaling for high-res displays
            print("Could not detect Windows Scale, assuming 125%")
            self.cached_scale_factor = 1.25
            return 1.25

        except Exception as e:
            print(f"Windows Scale detection error: {e}")
            self.cached_scale_factor = 1.25
            return 1.25

    def get_available_monitors(self):
        """Get list of available monitors with their physical resolutions and positions"""
        monitors = []

        try:
            user32 = ctypes.windll.user32

            # Define DEVMODE structure for getting physical resolution
            class DEVMODE(ctypes.Structure):
                _fields_ = [
                    ('dmDeviceName', ctypes.c_wchar * 32),
                    ('dmSpecVersion', wintypes.WORD),
                    ('dmDriverVersion', wintypes.WORD),
                    ('dmSize', wintypes.WORD),
                    ('dmDriverExtra', wintypes.WORD),
                    ('dmFields', wintypes.DWORD),
                    ('dmOrientation', ctypes.c_short),
                    ('dmPaperSize', ctypes.c_short),
                    ('dmPaperLength', ctypes.c_short),
                    ('dmPaperWidth', ctypes.c_short),
                    ('dmScale', ctypes.c_short),
                    ('dmCopies', ctypes.c_short),
                    ('dmDefaultSource', ctypes.c_short),
                    ('dmPrintQuality', ctypes.c_short),
                    ('dmColor', ctypes.c_short),
                    ('dmDuplex', ctypes.c_short),
                    ('dmYResolution', ctypes.c_short),
                    ('dmTTOption', ctypes.c_short),
                    ('dmCollate', ctypes.c_short),
                    ('dmFormName', ctypes.c_wchar * 32),
                    ('dmLogPixels', wintypes.WORD),
                    ('dmBitsPerPel', wintypes.DWORD),
                    ('dmPelsWidth', wintypes.DWORD),
                    ('dmPelsHeight', wintypes.DWORD),
                    ('dmDisplayFlags', wintypes.DWORD),
                    ('dmDisplayFrequency', wintypes.DWORD),
                ]

            # Use EnumDisplayMonitors to get all monitors with positions
            monitor_list = []

            def monitor_enum_proc(hmonitor, hdc, rect, data):
                try:
                    # Get monitor info structure
                    class MONITORINFOEX(ctypes.Structure):
                        _fields_ = [
                            ('cbSize', wintypes.DWORD),
                            ('rcMonitor', wintypes.RECT),
                            ('rcWork', wintypes.RECT),
                            ('dwFlags', wintypes.DWORD),
                            ('szDevice', ctypes.c_wchar * 32),
                        ]

                    monitor_info = MONITORINFOEX()
                    monitor_info.cbSize = ctypes.sizeof(MONITORINFOEX)

                    if user32.GetMonitorInfoW(hmonitor, ctypes.byref(monitor_info)):
                        # Get physical position and size
                        left = monitor_info.rcMonitor.left
                        top = monitor_info.rcMonitor.top
                        right = monitor_info.rcMonitor.right
                        bottom = monitor_info.rcMonitor.bottom
                        width = right - left
                        height = bottom - top

                        # Get physical resolution for this monitor
                        devmode = DEVMODE()
                        devmode.dmSize = ctypes.sizeof(DEVMODE)

                        if user32.EnumDisplaySettingsW(monitor_info.szDevice, -1, ctypes.byref(devmode)):
                            physical_width = devmode.dmPelsWidth
                            physical_height = devmode.dmPelsHeight
                        else:
                            physical_width = width
                            physical_height = height

                        is_primary = (monitor_info.dwFlags & 1) != 0  # MONITORINFOF_PRIMARY

                        monitor_list.append({
                            'name': 'Primary Monitor' if is_primary else f'Monitor {len(monitor_list) + 1}',
                            'width': physical_width,
                            'height': physical_height,
                            'left': left,
                            'top': top,
                            'is_primary': is_primary,
                            'monitor_index': len(monitor_list),
                            'device_name': monitor_info.szDevice
                        })

                        print(f"Found monitor: {monitor_info.szDevice} - {physical_width}x{physical_height} at ({left}, {top})")

                except Exception as e:
                    print(f"Error processing monitor: {e}")
                return True

            # Define the callback type
            MONITORENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HMONITOR, wintypes.HDC, ctypes.POINTER(wintypes.RECT), wintypes.LPARAM)
            enum_proc = MONITORENUMPROC(monitor_enum_proc)

            # Enumerate all monitors
            user32.EnumDisplayMonitors(None, None, enum_proc, 0)

            # Sort monitors: primary first, then by position
            monitor_list.sort(key=lambda m: (not m['is_primary'], m['left'], m['top']))
            monitors = monitor_list

        except Exception as e:
            print(f"Error detecting monitors: {e}")
            # Fallback to single monitor
            monitors = [{
                'name': 'Default Monitor',
                'width': 1920,
                'height': 1080,
                'is_primary': True,
                'monitor_index': 0,
                'left': 0,
                'top': 0
            }]

        print(f"Detected {len(monitors)} monitors:")
        for i, monitor in enumerate(monitors):
            pos_info = f" at ({monitor.get('left', 0)}, {monitor.get('top', 0)})" if not monitor['is_primary'] else ""
            print(f"  {i}: {monitor['name']} - {monitor['width']}x{monitor['height']}{pos_info}")

        return monitors

    def setup_display(self):
        """Setup display with proper resolution handling"""
        try:
            # Set DPI awareness first
            shcore = ctypes.windll.shcore
            shcore.SetProcessDpiAwareness(2)  # Per-monitor DPI aware v2
            print("DPI awareness set to per-monitor v2")
        except:
            try:
                shcore = ctypes.windll.shcore
                shcore.SetProcessDpiAwareness(1)  # System DPI aware
                print("DPI awareness set to system aware")
            except:
                print("Could not set DPI awareness")

        # Use custom resolution if set, otherwise use selected monitor
        if self.custom_resolution:
            self.SCREEN_WIDTH = self.custom_resolution[0]
            self.SCREEN_HEIGHT = self.custom_resolution[1]
            print(f"Using custom resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")
        else:
            monitor = self.available_monitors[self.current_monitor]
            # Always use the physical resolution from monitor detection
            self.SCREEN_WIDTH = monitor['width']
            self.SCREEN_HEIGHT = monitor['height']
            print(f"Using monitor physical resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")

        # Position window on selected monitor
        self.position_window_on_monitor()

    def position_window_on_monitor(self):
        """Position the game window on the selected monitor"""
        try:
            monitor = self.available_monitors[self.current_monitor]

            # Get window handle with retry logic
            import time
            hwnd = None
            for attempt in range(10):  # Try up to 10 times
                try:
                    wm_info = pygame.display.get_wm_info()
                    if "window" in wm_info and wm_info["window"]:
                        hwnd = wm_info["window"]
                        break
                except:
                    pass
                time.sleep(0.05)  # Wait 50ms between attempts

            if not hwnd:
                print("Could not get window handle for positioning")
                return

            user32 = ctypes.windll.user32

            # Get monitor position
            x = monitor.get('left', 0)
            y = monitor.get('top', 0)

            print(f"Positioning window on monitor {self.current_monitor + 1} ({monitor['name']}) at ({x}, {y})")
            print(f"Window size: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")

            # Set window position and size
            # Use SWP_NOZORDER to maintain topmost status, SWP_SHOWWINDOW to ensure visibility
            SWP_NOZORDER = 0x0004
            SWP_SHOWWINDOW = 0x0040
            result = user32.SetWindowPos(hwnd, 0, x, y, self.SCREEN_WIDTH, self.SCREEN_HEIGHT, SWP_NOZORDER | SWP_SHOWWINDOW)

            if result:
                print(f"Successfully positioned window on monitor {self.current_monitor + 1}")
            else:
                print(f"Failed to position window (error code: {user32.GetLastError()})")

        except Exception as e:
            print(f"Error positioning window: {e}")

    def setup_transparent_window(self):
        """Setup transparent overlay window on desktop"""
        try:
            # Get pygame window handle
            hwnd = pygame.display.get_wm_info()["window"]

            # Windows API constants
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x80000
            WS_EX_TRANSPARENT = 0x20
            WS_EX_TOPMOST = 0x8
            LWA_COLORKEY = 0x1

            # Get Windows API functions
            user32 = ctypes.windll.user32

            # Set window as layered and topmost
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE,
                                WS_EX_LAYERED | WS_EX_TOPMOST)

            # Set color key for transparency (black will be transparent)
            user32.SetLayeredWindowAttributes(hwnd, 0x000000, 0, LWA_COLORKEY)

            print("Transparent overlay window setup successful!")

        except Exception as e:
            print(f"Could not setup transparent window: {e}")
            print("Running in normal window mode")
        
    def get_player_name(self):
        """Get player name input with display settings"""
        input_text = ""
        input_active = True

        # Create a non-transparent window for name input
        temp_screen = pygame.display.set_mode((600, 400), pygame.NOFRAME)

        # Make this window non-transparent and topmost
        try:
            hwnd = pygame.display.get_wm_info()["window"]
            user32 = ctypes.windll.user32

            # Remove transparency and make topmost
            GWL_EXSTYLE = -20
            WS_EX_TOPMOST = 0x8
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE, WS_EX_TOPMOST)

            # Center the window
            screen_width = user32.GetSystemMetrics(0)
            screen_height = user32.GetSystemMetrics(1)
            window_x = (screen_width - 600) // 2
            window_y = (screen_height - 400) // 2
            user32.SetWindowPos(hwnd, 0, window_x, window_y, 600, 400, 0)
        except:
            pass

        while input_active:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_RETURN:
                        if input_text.strip():
                            # Apply display settings and reinitialize
                            self.setup_display()

                            # Restore original screen with new settings
                            self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
                            self.setup_transparent_window()

                            # Position window on selected monitor
                            self.position_window_on_monitor()

                            print(f"Game initialized with resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT} on monitor {self.current_monitor + 1}")
                            return input_text.strip()
                    elif event.key == pygame.K_BACKSPACE:
                        input_text = input_text[:-1]
                    else:
                        input_text += event.unicode
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        mouse_pos = pygame.mouse.get_pos()

                        # Define button rectangles (matching UI theme coordinates)
                        monitor_y = 260  # 220 + 40
                        res_y = 295     # monitor_y + 35

                        prev_mon_rect = pygame.Rect(150, monitor_y, 35, 25)
                        next_mon_rect = pygame.Rect(420, monitor_y, 35, 25)
                        prev_res_rect = pygame.Rect(150, res_y, 35, 25)
                        next_res_rect = pygame.Rect(420, res_y, 35, 25)

                        if prev_mon_rect.collidepoint(mouse_pos):
                            self.current_monitor = (self.current_monitor - 1) % len(self.available_monitors)
                            self.custom_resolution = None
                            print(f"Switched to monitor {self.current_monitor + 1}")
                        elif next_mon_rect.collidepoint(mouse_pos):
                            self.current_monitor = (self.current_monitor + 1) % len(self.available_monitors)
                            self.custom_resolution = None
                            print(f"Switched to monitor {self.current_monitor + 1}")
                        elif prev_res_rect.collidepoint(mouse_pos):
                            # Cycle through common resolutions (minimum 1280x720)
                            current_monitor = self.available_monitors[self.current_monitor]
                            common_resolutions = [
                                (current_monitor['width'], current_monitor['height']),
                                (1280, 720),
                                (1920, 1080),
                                (2560, 1440),
                                (3440, 1440),
                                (3840, 2160)
                            ]
                            # Remove duplicates and sort
                            common_resolutions = list(set(common_resolutions))
                            common_resolutions.sort(key=lambda x: x[0] * x[1])

                            if self.custom_resolution:
                                try:
                                    current_index = common_resolutions.index(self.custom_resolution)
                                    new_index = (current_index - 1) % len(common_resolutions)
                                    self.custom_resolution = common_resolutions[new_index]
                                except ValueError:
                                    self.custom_resolution = common_resolutions[0]
                            else:
                                self.custom_resolution = common_resolutions[-1]
                            print(f"Resolution changed to: {self.custom_resolution}")
                        elif next_res_rect.collidepoint(mouse_pos):
                            # Cycle through common resolutions (minimum 1280x720)
                            current_monitor = self.available_monitors[self.current_monitor]
                            common_resolutions = [
                                (current_monitor['width'], current_monitor['height']),
                                (1280, 720),
                                (1920, 1080),
                                (2560, 1440),
                                (3440, 1440),
                                (3840, 2160)
                            ]
                            # Remove duplicates and sort
                            common_resolutions = list(set(common_resolutions))
                            common_resolutions.sort(key=lambda x: x[0] * x[1])

                            if self.custom_resolution:
                                try:
                                    current_index = common_resolutions.index(self.custom_resolution)
                                    new_index = (current_index + 1) % len(common_resolutions)
                                    self.custom_resolution = common_resolutions[new_index]
                                except ValueError:
                                    self.custom_resolution = common_resolutions[1]
                            else:
                                self.custom_resolution = common_resolutions[1]
                            print(f"Resolution changed to: {self.custom_resolution}")

            # Use UI theme to draw startup screen
            self.ui_theme.draw_startup_screen(temp_screen, input_text, self.current_monitor,
                                            self.available_monitors, self.custom_resolution)

            pygame.display.flip()
            self.clock.tick(self.FPS)
    
    def initialize_game(self, player_name):
        """Initialize game components"""
        # Load map
        map_data = self.map_loader.load_map(0)
        self.game_state.current_map = map_data
        self.game_state.player_name = player_name
        
        # Create player
        player_x = self.SCREEN_WIDTH // 2
        player_y = self.SCREEN_HEIGHT // 2
        self.player = Player(player_x, player_y, self.image_handler, self.sound_manager)
        
        # Create monster manager
        self.monster_manager = MonsterManager(
            self.SCREEN_WIDTH, 
            self.SCREEN_HEIGHT, 
            map_data, 
            self.image_handler
        )
    
    def draw_info_panel(self):
        """Draw the information panel"""
        # Create a sci-fi themed panel surface
        panel_surface = pygame.Surface((self.info_panel_rect.width, self.info_panel_rect.height))
        panel_surface.fill(self.DARK_BLUE)  # Dark blue background

        # Draw sci-fi border with multiple layers
        pygame.draw.rect(panel_surface, self.CYAN, (0, 0, self.info_panel_rect.width, self.info_panel_rect.height), 2)
        pygame.draw.rect(panel_surface, self.NEON_BLUE, (2, 2, self.info_panel_rect.width - 4, self.info_panel_rect.height - 4), 1)

        # Draw corner accents
        corner_size = 10
        for corner in [(0, 0), (self.info_panel_rect.width - corner_size, 0),
                      (0, self.info_panel_rect.height - corner_size),
                      (self.info_panel_rect.width - corner_size, self.info_panel_rect.height - corner_size)]:
            pygame.draw.rect(panel_surface, self.ORANGE, (corner[0], corner[1], corner_size, corner_size))

        # Blit the sci-fi panel to screen
        self.screen.blit(panel_surface, self.info_panel_rect.topleft)
        
        # Player info - draw relative to panel position
        panel_x = self.info_panel_rect.x
        panel_y = self.info_panel_rect.y
        y_offset = panel_y + 20

        # Player name with sci-fi styling
        name_text = self.font.render(f"OPERATIVE: {self.game_state.player_name}", True, self.CYAN)
        self.screen.blit(name_text, (panel_x + 10, y_offset))
        y_offset += 25

        # HP with color coding
        hp_color = self.NEON_GREEN if self.player.hp > 50 else (self.ORANGE if self.player.hp > 20 else self.RED)
        hp_text = self.font.render(f"VITALS: {self.player.hp}/{self.player.max_hp}", True, hp_color)
        self.screen.blit(hp_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Level
        level_text = self.font.render(f"RANK: {self.player.level}", True, self.NEON_GREEN)
        self.screen.blit(level_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Map name
        map_name = self.game_state.current_map.get('map_name', 'SECTOR-01')
        map_text = self.font.render(f"SECTOR: {map_name}", True, self.ORANGE)
        self.screen.blit(map_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Monster count
        monster_count = len(self.monster_manager.monsters)
        threat_color = self.RED if monster_count > 5 else (self.ORANGE if monster_count > 2 else self.NEON_GREEN)
        monster_text = self.font.render(f"THREATS: {monster_count}", True, threat_color)
        self.screen.blit(monster_text, (panel_x + 10, y_offset))

        # Sci-fi styled options button
        button_x = panel_x + self.options_button_rect.x
        button_y = panel_y + self.options_button_rect.y
        button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)

        # Button background with sci-fi styling
        button_color = self.DARK_GRAY if not self.show_options else self.NEON_BLUE
        pygame.draw.rect(self.screen, button_color, button_rect)
        pygame.draw.rect(self.screen, self.CYAN, button_rect, 2)

        # Add corner highlights
        pygame.draw.rect(self.screen, self.ORANGE, (button_rect.x, button_rect.y, 5, 5))
        pygame.draw.rect(self.screen, self.ORANGE, (button_rect.x + button_rect.width - 5, button_rect.y, 5, 5))

        # Button text
        button_text = self.small_font.render("CONFIG", True, self.WHITE)
        text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, text_rect)

        # Options panel (if shown)
        if self.show_options:
            # Expand panel height when options are shown (larger to fit all controls)
            expanded_panel_height = 400
            options_rect = pygame.Rect(panel_x, panel_y + 180, 400, 220)

            # Draw expanded panel background
            expanded_surface = pygame.Surface((400, expanded_panel_height))
            expanded_surface.fill((32, 32, 32))  # Dark gray, not black
            pygame.draw.rect(expanded_surface, self.WHITE, (0, 0, 400, expanded_panel_height), 2)
            self.screen.blit(expanded_surface, (panel_x, panel_y))

            # Redraw all panel content on expanded surface
            y_offset = 20
            name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
            self.screen.blit(name_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
            self.screen.blit(hp_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
            self.screen.blit(level_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            map_name = self.game_state.current_map.get('map_name', 'Unknown')
            map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
            self.screen.blit(map_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            monster_count = len(self.monster_manager.monsters)
            monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
            self.screen.blit(monster_text, (panel_x + 10, panel_y + y_offset))

            # Redraw options button
            button_x = panel_x + self.options_button_rect.x
            button_y = panel_y + self.options_button_rect.y
            button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)
            pygame.draw.rect(self.screen, self.GRAY, button_rect)
            pygame.draw.rect(self.screen, self.WHITE, button_rect, 2)
            button_text = self.small_font.render("Options", True, self.WHITE)
            text_rect = button_text.get_rect(center=button_rect.center)
            self.screen.blit(button_text, text_rect)

            # Use UI theme to draw options panel and store button rectangles
            self.options_button_rects = self.ui_theme.draw_options_panel(
                self.screen, options_rect, self.sound_manager, self.current_monitor,
                self.available_monitors, self.custom_resolution, self.texture_zoom,
                self.editing_width, self.editing_height, self.custom_width_input, self.custom_height_input
            )

    def restart_game(self):
        """Restart the game with new display settings"""
        print("Restarting game with new settings...")

        # Update display settings
        self.setup_display()

        # Recreate the screen
        pygame.display.quit()
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Setup transparent window again
        self.setup_transparent_window()

        # Position window on selected monitor
        self.position_window_on_monitor()

        # Reset UI state
        self.show_options = False

        # Reinitialize game objects with new screen size
        if self.player:
            # Keep player in bounds
            self.player.x = min(self.player.x, self.SCREEN_WIDTH - 50)
            self.player.y = min(self.player.y, self.SCREEN_HEIGHT - 50)
            self.player.x = max(50, self.player.x)
            self.player.y = max(50, self.player.y)

        print(f"Game restarted with resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT} on monitor {self.current_monitor + 1}")



    def run(self):
        """Main game loop"""
        # Get player name
        player_name = self.get_player_name()
        
        # Initialize game
        self.initialize_game(player_name)
        
        running = True
        while running:
            dt = self.clock.tick(self.FPS) / 1000.0  # Delta time in seconds
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif self.editing_width or self.editing_height:
                        # Handle custom resolution input
                        if event.key == pygame.K_BACKSPACE:
                            if self.editing_width:
                                self.custom_width_input = self.custom_width_input[:-1]
                            elif self.editing_height:
                                self.custom_height_input = self.custom_height_input[:-1]
                        elif event.key == pygame.K_RETURN:
                            self.editing_width = False
                            self.editing_height = False
                        elif event.unicode.isdigit() and len(event.unicode) == 1:
                            if self.editing_width and len(self.custom_width_input) < 5:
                                self.custom_width_input += event.unicode
                            elif self.editing_height and len(self.custom_height_input) < 5:
                                self.custom_height_input += event.unicode
                    elif self.show_options:
                        # Arrow keys for volume control
                        if event.key == pygame.K_LEFT:
                            new_volume = max(0.0, self.sound_manager.volume - 0.1)
                            self.sound_manager.set_volume(new_volume)
                        elif event.key == pygame.K_RIGHT:
                            new_volume = min(1.0, self.sound_manager.volume + 0.1)
                            self.sound_manager.set_volume(new_volume)
                        elif event.key == pygame.K_UP and self.show_options:
                            # Previous monitor
                            self.current_monitor = (self.current_monitor - 1) % len(self.available_monitors)
                            self.custom_resolution = None
                        elif event.key == pygame.K_DOWN and self.show_options:
                            # Next monitor
                            self.current_monitor = (self.current_monitor + 1) % len(self.available_monitors)
                            self.custom_resolution = None
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        mouse_pos = pygame.mouse.get_pos()
                        handled = False



                        # Check options button first
                        button_x = self.info_panel_rect.x + self.options_button_rect.x
                        button_y = self.info_panel_rect.y + self.options_button_rect.y
                        button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)

                        if button_rect.collidepoint(mouse_pos):
                            self.show_options = not self.show_options
                            handled = True
                        elif self.show_options:
                            # Check if clicking in options area (updated size)
                            options_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y + 180, 400, 220)
                            expanded_panel_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y, 400, 400)

                            if options_rect.collidepoint(mouse_pos):
                                # Get button rectangles from stored options button rects
                                sound_area = pygame.Rect(options_rect.x + 10, options_rect.y + 10, 200, 20)
                                vol_minus_rect = self.options_button_rects['vol_minus_rect']
                                vol_plus_rect = self.options_button_rects['vol_plus_rect']
                                prev_mon_rect = self.options_button_rects['prev_mon_rect']
                                next_mon_rect = self.options_button_rects['next_mon_rect']
                                prev_res_rect = self.options_button_rects['prev_res_rect']
                                next_res_rect = self.options_button_rects['next_res_rect']
                                width_input_rect = self.options_button_rects['width_input_rect']
                                height_input_rect = self.options_button_rects['height_input_rect']
                                apply_res_rect = self.options_button_rects['apply_res_rect']
                                zoom_minus_rect = self.options_button_rects['zoom_minus_rect']
                                zoom_plus_rect = self.options_button_rects['zoom_plus_rect']
                                save_btn_rect = self.options_button_rects['save_btn_rect']
                                close_btn_rect = self.options_button_rects['close_btn_rect']

                                if sound_area.collidepoint(mouse_pos):
                                    self.sound_manager.toggle_sound()
                                elif vol_minus_rect.collidepoint(mouse_pos):
                                    new_volume = max(0.0, self.sound_manager.volume - 0.1)
                                    self.sound_manager.set_volume(new_volume)
                                elif vol_plus_rect.collidepoint(mouse_pos):
                                    new_volume = min(1.0, self.sound_manager.volume + 0.1)
                                    self.sound_manager.set_volume(new_volume)
                                elif prev_mon_rect.collidepoint(mouse_pos):
                                    self.current_monitor = (self.current_monitor - 1) % len(self.available_monitors)
                                    self.custom_resolution = None
                                elif next_mon_rect.collidepoint(mouse_pos):
                                    self.current_monitor = (self.current_monitor + 1) % len(self.available_monitors)
                                    self.custom_resolution = None
                                elif prev_res_rect.collidepoint(mouse_pos):
                                    # Cycle through resolutions (minimum 1280x720)
                                    current_monitor = self.available_monitors[self.current_monitor]
                                    common_resolutions = [
                                        (current_monitor['width'], current_monitor['height']),
                                        (1280, 720),
                                        (1920, 1080),
                                        (2560, 1440),
                                        (3440, 1440),
                                        (3840, 2160)
                                    ]
                                    # Remove duplicates and sort
                                    common_resolutions = list(set(common_resolutions))
                                    common_resolutions.sort(key=lambda x: x[0] * x[1])

                                    if self.custom_resolution:
                                        try:
                                            current_index = common_resolutions.index(self.custom_resolution)
                                            new_index = (current_index - 1) % len(common_resolutions)
                                            self.custom_resolution = common_resolutions[new_index]
                                        except ValueError:
                                            self.custom_resolution = common_resolutions[0]
                                    else:
                                        self.custom_resolution = common_resolutions[-1]
                                elif next_res_rect.collidepoint(mouse_pos):
                                    # Cycle through resolutions (minimum 1280x720)
                                    current_monitor = self.available_monitors[self.current_monitor]
                                    common_resolutions = [
                                        (current_monitor['width'], current_monitor['height']),
                                        (1280, 720),
                                        (1920, 1080),
                                        (2560, 1440),
                                        (3440, 1440),
                                        (3840, 2160)
                                    ]
                                    # Remove duplicates and sort
                                    common_resolutions = list(set(common_resolutions))
                                    common_resolutions.sort(key=lambda x: x[0] * x[1])

                                    if self.custom_resolution:
                                        try:
                                            current_index = common_resolutions.index(self.custom_resolution)
                                            new_index = (current_index + 1) % len(common_resolutions)
                                            self.custom_resolution = common_resolutions[new_index]
                                        except ValueError:
                                            self.custom_resolution = common_resolutions[1]
                                    else:
                                        self.custom_resolution = common_resolutions[1]
                                elif zoom_minus_rect.collidepoint(mouse_pos):
                                    # Decrease zoom and restart
                                    self.texture_zoom = max(self.min_zoom, self.texture_zoom - self.zoom_step)
                                    self.image_handler.set_texture_zoom(self.texture_zoom)
                                    print(f"Zoom decreased to {int(self.texture_zoom * 100)}%")
                                    self.restart_game()
                                elif zoom_plus_rect.collidepoint(mouse_pos):
                                    # Increase zoom and restart
                                    self.texture_zoom = min(self.max_zoom, self.texture_zoom + self.zoom_step)
                                    self.image_handler.set_texture_zoom(self.texture_zoom)
                                    print(f"Zoom increased to {int(self.texture_zoom * 100)}%")
                                    self.restart_game()
                                elif width_input_rect.collidepoint(mouse_pos):
                                    self.editing_width = True
                                    self.editing_height = False
                                elif height_input_rect.collidepoint(mouse_pos):
                                    self.editing_height = True
                                    self.editing_width = False
                                elif apply_res_rect.collidepoint(mouse_pos):
                                    # Apply custom resolution
                                    try:
                                        width = int(self.custom_width_input) if self.custom_width_input else 0
                                        height = int(self.custom_height_input) if self.custom_height_input else 0
                                        if width > 0 and height > 0:
                                            self.custom_resolution = (width, height)
                                            print(f"Custom resolution set: {width}x{height}")
                                    except ValueError:
                                        print("Invalid resolution values")
                                elif save_btn_rect.collidepoint(mouse_pos):
                                    self.restart_game()
                                else:
                                    self.editing_width = False
                                    self.editing_height = False
                                handled = True
                            elif not expanded_panel_rect.collidepoint(mouse_pos):
                                self.show_options = False
                                self.editing_width = False
                                self.editing_height = False

                        if not handled:
                            # Check if clicking on panel (consider expanded size if options are shown)
                            panel_rect = self.info_panel_rect
                            if self.show_options:
                                panel_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y, 400, 400)

                            if panel_rect.collidepoint(mouse_pos):
                                # Start dragging panel
                                self.panel_dragging = True
                                self.panel_drag_offset_x = mouse_pos[0] - self.info_panel_rect.x
                                self.panel_drag_offset_y = mouse_pos[1] - self.info_panel_rect.y
                                print(f"Panel drag started at {mouse_pos}")
                            else:
                                # Try to drag player - always try if nothing else was clicked
                                if self.player.start_drag(mouse_pos):
                                    print("Player drag started")
                                else:
                                    print(f"No drag target found at {mouse_pos}")
                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # Left mouse button
                        self.panel_dragging = False
                        self.player.stop_drag()
                elif event.type == pygame.MOUSEMOTION:
                    mouse_pos = pygame.mouse.get_pos()
                    if self.panel_dragging:
                        # Move panel
                        new_x = mouse_pos[0] - self.panel_drag_offset_x
                        new_y = mouse_pos[1] - self.panel_drag_offset_y
                        self.info_panel_rect.x = new_x
                        self.info_panel_rect.y = new_y
                        print(f"Panel moved to {new_x}, {new_y}")
                    elif self.player.dragging:
                        self.player.update_drag(mouse_pos)
            
            # Store previous HP values for damage detection
            prev_player_hp = self.player.hp
            prev_monster_hp = {id(m): m.hp for m in self.monster_manager.monsters}

            # Update game objects
            self.player.update(dt, self.monster_manager.monsters)
            self.monster_manager.update(dt, self.player)
            self.damage_display.update(dt)

            # Check for damage and add damage text
            # Player damage
            if self.player.hp < prev_player_hp:
                damage = prev_player_hp - self.player.hp
                self.damage_display.add_damage_text(self.player.x, self.player.y - 20, damage, is_player=True)

            # Monster damage
            for monster in self.monster_manager.monsters:
                monster_id = id(monster)
                if monster_id in prev_monster_hp and monster.hp < prev_monster_hp[monster_id]:
                    damage = prev_monster_hp[monster_id] - monster.hp
                    self.damage_display.add_damage_text(monster.x, monster.y - 20, damage, is_player=False)
            
            # Clear screen with transparent black
            self.screen.fill((0, 0, 0))
            
            # Draw game objects
            self.player.draw(self.screen)
            self.monster_manager.draw(self.screen)

            # Draw damage numbers
            self.damage_display.draw(self.screen)

            # Draw UI
            self.draw_info_panel()



            # Update display
            pygame.display.flip()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = MarineEXE()
    game.run()
