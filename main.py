import pygame
import sys
import os
import ctypes
from ctypes import wintypes
from utility.game_state import Game<PERSON><PERSON>
from utility.player import Player
from utility.monster import <PERSON><PERSON>anager
from utility.map_loader import MapLoader
from utility.image_handler import ImageHandler
from utility.damage_display import DamageDisplayManager
from utility.sound_manager import SoundManager

class MarineEXE:
    def __init__(self):
        pygame.init()
        pygame.mixer.init()  # Initialize mixer for sound

        # Initialize display settings first
        self.current_monitor = 0
        self.custom_resolution = None

        # Manual DPI scaling
        self.manual_dpi_scaling = None
        self.dpi_input = ""
        self.editing_dpi = False

        # Custom resolution input
        self.custom_width_input = ""
        self.custom_height_input = ""
        self.editing_width = False
        self.editing_height = False

        # Cache the scale factor to avoid repeated detection
        self.cached_scale_factor = None

        # Zoom functionality
        self.texture_zoom = 1.0  # Default zoom level
        self.min_zoom = 0.5
        self.max_zoom = 3.0
        self.zoom_step = 0.25

        # Now get available monitors (after manual_dpi_scaling is initialized)
        self.available_monitors = self.get_available_monitors()

        # Get desktop size with proper DPI handling
        self.setup_display()

        print(f"Using resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")
        print(f"Available monitors: {len(self.available_monitors)}")

        # Create fullscreen window
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Make window transparent and overlay on desktop
        self.setup_transparent_window()
        
        # Game clock
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Game components
        self.game_state = GameState()
        self.image_handler = ImageHandler()
        self.image_handler.set_texture_zoom(self.texture_zoom)  # Initialize zoom
        self.map_loader = MapLoader()
        self.sound_manager = SoundManager()

        # Game objects
        self.player = None
        self.monster_manager = None
        self.damage_display = DamageDisplayManager()

        # Colors
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.GRAY = (128, 128, 128)
        self.GREEN = (0, 255, 0)
        self.RED = (255, 0, 0)
        self.BLUE = (0, 100, 255)
        self.TRANSPARENT = (0, 0, 0, 0)

        # UI
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        self.info_panel_rect = pygame.Rect(10, 10, 400, 180)  # Made taller for options button
        self.panel_dragging = False
        self.panel_drag_offset_x = 0
        self.panel_drag_offset_y = 0

        # Options button (relative to panel)
        self.options_button_rect = pygame.Rect(320, 150, 70, 25)
        self.show_options = False

    def get_windows_scale_factor(self):
        """Get Windows Scale and Layout setting"""
        # Use manual scaling if set
        if self.manual_dpi_scaling:
            return self.manual_dpi_scaling

        # Return cached value if available
        if self.cached_scale_factor:
            return self.cached_scale_factor

        try:
            user32 = ctypes.windll.user32

            # Method 1: Get actual vs scaled resolution
            try:
                # Get what Windows reports (scaled)
                scaled_width = user32.GetSystemMetrics(0)
                scaled_height = user32.GetSystemMetrics(1)

                # Get actual physical resolution
                class DEVMODE(ctypes.Structure):
                    _fields_ = [
                        ('dmDeviceName', ctypes.c_wchar * 32),
                        ('dmSpecVersion', wintypes.WORD),
                        ('dmDriverVersion', wintypes.WORD),
                        ('dmSize', wintypes.WORD),
                        ('dmDriverExtra', wintypes.WORD),
                        ('dmFields', wintypes.DWORD),
                        ('dmOrientation', ctypes.c_short),
                        ('dmPaperSize', ctypes.c_short),
                        ('dmPaperLength', ctypes.c_short),
                        ('dmPaperWidth', ctypes.c_short),
                        ('dmScale', ctypes.c_short),
                        ('dmCopies', ctypes.c_short),
                        ('dmDefaultSource', ctypes.c_short),
                        ('dmPrintQuality', ctypes.c_short),
                        ('dmColor', ctypes.c_short),
                        ('dmDuplex', ctypes.c_short),
                        ('dmYResolution', ctypes.c_short),
                        ('dmTTOption', ctypes.c_short),
                        ('dmCollate', ctypes.c_short),
                        ('dmFormName', ctypes.c_wchar * 32),
                        ('dmLogPixels', wintypes.WORD),
                        ('dmBitsPerPel', wintypes.DWORD),
                        ('dmPelsWidth', wintypes.DWORD),
                        ('dmPelsHeight', wintypes.DWORD),
                        ('dmDisplayFlags', wintypes.DWORD),
                        ('dmDisplayFrequency', wintypes.DWORD),
                    ]

                devmode = DEVMODE()
                devmode.dmSize = ctypes.sizeof(DEVMODE)

                if user32.EnumDisplaySettingsW(None, -1, ctypes.byref(devmode)):
                    actual_width = devmode.dmPelsWidth
                    actual_height = devmode.dmPelsHeight

                    scale_factor = actual_width / scaled_width
                    scale_percent = int(scale_factor * 100)

                    print(f"Windows Scale and Layout detected: {scale_percent}%")
                    print(f"Physical resolution: {actual_width}x{actual_height}")
                    print(f"Scaled resolution: {scaled_width}x{scaled_height}")

                    # Cache the result
                    self.cached_scale_factor = scale_factor
                    return scale_factor
            except Exception as e:
                print(f"Scale detection failed: {e}")

            # Fallback: assume 125% scaling for high-res displays
            print("Could not detect Windows Scale, assuming 125%")
            self.cached_scale_factor = 1.25
            return 1.25

        except Exception as e:
            print(f"Windows Scale detection error: {e}")
            self.cached_scale_factor = 1.25
            return 1.25

    def get_available_monitors(self):
        """Get list of available monitors with their physical resolutions and positions"""
        monitors = []

        try:
            user32 = ctypes.windll.user32

            # Define DEVMODE structure for getting physical resolution
            class DEVMODE(ctypes.Structure):
                _fields_ = [
                    ('dmDeviceName', ctypes.c_wchar * 32),
                    ('dmSpecVersion', wintypes.WORD),
                    ('dmDriverVersion', wintypes.WORD),
                    ('dmSize', wintypes.WORD),
                    ('dmDriverExtra', wintypes.WORD),
                    ('dmFields', wintypes.DWORD),
                    ('dmOrientation', ctypes.c_short),
                    ('dmPaperSize', ctypes.c_short),
                    ('dmPaperLength', ctypes.c_short),
                    ('dmPaperWidth', ctypes.c_short),
                    ('dmScale', ctypes.c_short),
                    ('dmCopies', ctypes.c_short),
                    ('dmDefaultSource', ctypes.c_short),
                    ('dmPrintQuality', ctypes.c_short),
                    ('dmColor', ctypes.c_short),
                    ('dmDuplex', ctypes.c_short),
                    ('dmYResolution', ctypes.c_short),
                    ('dmTTOption', ctypes.c_short),
                    ('dmCollate', ctypes.c_short),
                    ('dmFormName', ctypes.c_wchar * 32),
                    ('dmLogPixels', wintypes.WORD),
                    ('dmBitsPerPel', wintypes.DWORD),
                    ('dmPelsWidth', wintypes.DWORD),
                    ('dmPelsHeight', wintypes.DWORD),
                    ('dmDisplayFlags', wintypes.DWORD),
                    ('dmDisplayFrequency', wintypes.DWORD),
                ]

            # Use EnumDisplayMonitors to get all monitors with positions
            monitor_list = []

            def monitor_enum_proc(hmonitor, hdc, rect, data):
                try:
                    # Get monitor info structure
                    class MONITORINFOEX(ctypes.Structure):
                        _fields_ = [
                            ('cbSize', wintypes.DWORD),
                            ('rcMonitor', wintypes.RECT),
                            ('rcWork', wintypes.RECT),
                            ('dwFlags', wintypes.DWORD),
                            ('szDevice', ctypes.c_wchar * 32),
                        ]

                    monitor_info = MONITORINFOEX()
                    monitor_info.cbSize = ctypes.sizeof(MONITORINFOEX)

                    if user32.GetMonitorInfoW(hmonitor, ctypes.byref(monitor_info)):
                        # Get physical position and size
                        left = monitor_info.rcMonitor.left
                        top = monitor_info.rcMonitor.top
                        right = monitor_info.rcMonitor.right
                        bottom = monitor_info.rcMonitor.bottom
                        width = right - left
                        height = bottom - top

                        # Get physical resolution for this monitor
                        devmode = DEVMODE()
                        devmode.dmSize = ctypes.sizeof(DEVMODE)

                        if user32.EnumDisplaySettingsW(monitor_info.szDevice, -1, ctypes.byref(devmode)):
                            physical_width = devmode.dmPelsWidth
                            physical_height = devmode.dmPelsHeight
                        else:
                            physical_width = width
                            physical_height = height

                        is_primary = (monitor_info.dwFlags & 1) != 0  # MONITORINFOF_PRIMARY

                        monitor_list.append({
                            'name': 'Primary Monitor' if is_primary else f'Monitor {len(monitor_list) + 1}',
                            'width': physical_width,
                            'height': physical_height,
                            'left': left,
                            'top': top,
                            'is_primary': is_primary,
                            'monitor_index': len(monitor_list),
                            'device_name': monitor_info.szDevice
                        })

                        print(f"Found monitor: {monitor_info.szDevice} - {physical_width}x{physical_height} at ({left}, {top})")

                except Exception as e:
                    print(f"Error processing monitor: {e}")
                return True

            # Define the callback type
            MONITORENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HMONITOR, wintypes.HDC, ctypes.POINTER(wintypes.RECT), wintypes.LPARAM)
            enum_proc = MONITORENUMPROC(monitor_enum_proc)

            # Enumerate all monitors
            user32.EnumDisplayMonitors(None, None, enum_proc, 0)

            # Sort monitors: primary first, then by position
            monitor_list.sort(key=lambda m: (not m['is_primary'], m['left'], m['top']))
            monitors = monitor_list

            # Try to detect additional monitors using EnumDisplayDevices and EnumDisplaySettings
            try:
                device_index = 1
                while device_index < 10:  # Check up to 10 devices
                    class DISPLAY_DEVICE(ctypes.Structure):
                        _fields_ = [
                            ('cb', wintypes.DWORD),
                            ('DeviceName', ctypes.c_wchar * 32),
                            ('DeviceString', ctypes.c_wchar * 128),
                            ('StateFlags', wintypes.DWORD),
                            ('DeviceID', ctypes.c_wchar * 128),
                            ('DeviceKey', ctypes.c_wchar * 128),
                        ]

                    display_device = DISPLAY_DEVICE()
                    display_device.cb = ctypes.sizeof(DISPLAY_DEVICE)

                    if user32.EnumDisplayDevicesW(None, device_index, ctypes.byref(display_device), 0):
                        # Check if this device is active
                        if display_device.StateFlags & 0x00000001:  # DISPLAY_DEVICE_ACTIVE
                            # Get the resolution for this device
                            devmode = DEVMODE()
                            devmode.dmSize = ctypes.sizeof(DEVMODE)

                            if user32.EnumDisplaySettingsW(display_device.DeviceName, -1, ctypes.byref(devmode)):
                                width = devmode.dmPelsWidth
                                height = devmode.dmPelsHeight

                                # Only add if different from primary
                                if width != primary_physical_width or height != primary_physical_height:
                                    monitors.append({
                                        'name': f'Monitor {device_index + 1}',
                                        'width': width,
                                        'height': height,
                                        'is_primary': False,
                                        'monitor_index': device_index,
                                        'device_name': display_device.DeviceName
                                    })
                                    print(f"Secondary monitor {device_index + 1} physical resolution: {width}x{height}")
                    else:
                        break

                    device_index += 1

            except Exception as e:
                print(f"Error enumerating secondary monitors: {e}")

        except Exception as e:
            print(f"Error detecting monitors: {e}")
            # Fallback to single monitor
            monitors = [{
                'name': 'Default Monitor',
                'width': 1920,
                'height': 1080,
                'is_primary': True,
                'monitor_index': 0
            }]

        print(f"Detected {len(monitors)} monitors:")
        for i, monitor in enumerate(monitors):
            print(f"  {i}: {monitor['name']} - {monitor['width']}x{monitor['height']}")

        return monitors

    def setup_display(self):
        """Setup display"""
        try:
            # Set DPI awareness first
            shcore = ctypes.windll.shcore
            shcore.SetProcessDpiAwareness(1)  # Make DPI aware
            print("DPI awareness set")
        except:
            print("Could not set DPI awareness")

        # Use custom resolution if set, otherwise use selected monitor
        if self.custom_resolution:
            self.SCREEN_WIDTH = self.custom_resolution[0]
            self.SCREEN_HEIGHT = self.custom_resolution[1]
            print(f"Using custom resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")
        else:
            monitor = self.available_monitors[self.current_monitor]
            self.SCREEN_WIDTH = monitor['width']
            self.SCREEN_HEIGHT = monitor['height']
            print(f"Using monitor resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")

        # Position window on selected monitor
        self.position_window_on_monitor()

    def position_window_on_monitor(self):
        """Position the game window on the selected monitor"""
        try:
            monitor = self.available_monitors[self.current_monitor]

            # Get window handle
            hwnd = pygame.display.get_wm_info()["window"]
            user32 = ctypes.windll.user32

            # Position window
            if monitor.get('left') is not None and monitor.get('top') is not None:
                # Secondary monitor - use its position
                x = monitor['left']
                y = monitor['top']
                print(f"Positioning window on monitor {self.current_monitor + 1} at ({x}, {y})")
            else:
                # Primary monitor - center it
                x = 0
                y = 0
                print(f"Positioning window on primary monitor")

            # Set window position
            user32.SetWindowPos(hwnd, 0, x, y, self.SCREEN_WIDTH, self.SCREEN_HEIGHT, 0)

        except Exception as e:
            print(f"Error positioning window: {e}")

    def setup_transparent_window(self):
        """Setup transparent overlay window on desktop"""
        try:
            # Get pygame window handle
            hwnd = pygame.display.get_wm_info()["window"]

            # Windows API constants
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x80000
            WS_EX_TRANSPARENT = 0x20
            WS_EX_TOPMOST = 0x8
            LWA_COLORKEY = 0x1

            # Get Windows API functions
            user32 = ctypes.windll.user32

            # Set window as layered and topmost
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE,
                                WS_EX_LAYERED | WS_EX_TOPMOST)

            # Set color key for transparency (black will be transparent)
            user32.SetLayeredWindowAttributes(hwnd, 0x000000, 0, LWA_COLORKEY)

            print("Transparent overlay window setup successful!")

        except Exception as e:
            print(f"Could not setup transparent window: {e}")
            print("Running in normal window mode")
        
    def get_player_name(self):
        """Get player name input with display settings"""
        input_text = ""
        input_active = True

        # Create a non-transparent window for name input
        temp_screen = pygame.display.set_mode((600, 400), pygame.NOFRAME)

        # Make this window non-transparent and topmost
        try:
            hwnd = pygame.display.get_wm_info()["window"]
            user32 = ctypes.windll.user32

            # Remove transparency and make topmost
            GWL_EXSTYLE = -20
            WS_EX_TOPMOST = 0x8
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE, WS_EX_TOPMOST)

            # Center the window
            screen_width = user32.GetSystemMetrics(0)
            screen_height = user32.GetSystemMetrics(1)
            window_x = (screen_width - 600) // 2
            window_y = (screen_height - 400) // 2
            user32.SetWindowPos(hwnd, 0, window_x, window_y, 600, 400, 0)
        except:
            pass

        while input_active:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_RETURN:
                        if input_text.strip():
                            # Apply display settings and reinitialize
                            self.setup_display()

                            # Restore original screen with new settings
                            self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
                            self.setup_transparent_window()

                            # Position window on selected monitor
                            self.position_window_on_monitor()

                            print(f"Game initialized with resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT} on monitor {self.current_monitor + 1}")
                            return input_text.strip()
                    elif event.key == pygame.K_BACKSPACE:
                        input_text = input_text[:-1]
                    else:
                        input_text += event.unicode
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        mouse_pos = pygame.mouse.get_pos()

                        # Monitor selection arrows
                        prev_mon_rect = pygame.Rect(150, 280, 30, 25)
                        next_mon_rect = pygame.Rect(420, 280, 30, 25)

                        # Resolution arrows
                        prev_res_rect = pygame.Rect(150, 315, 30, 25)
                        next_res_rect = pygame.Rect(420, 315, 30, 25)

                        if prev_mon_rect.collidepoint(mouse_pos):
                            self.current_monitor = (self.current_monitor - 1) % len(self.available_monitors)
                            self.custom_resolution = None
                            print(f"Switched to monitor {self.current_monitor + 1}")
                        elif next_mon_rect.collidepoint(mouse_pos):
                            self.current_monitor = (self.current_monitor + 1) % len(self.available_monitors)
                            self.custom_resolution = None
                            print(f"Switched to monitor {self.current_monitor + 1}")
                        elif prev_res_rect.collidepoint(mouse_pos):
                            # Cycle through common resolutions (minimum 1280x720)
                            current_monitor = self.available_monitors[self.current_monitor]
                            common_resolutions = [
                                (current_monitor['width'], current_monitor['height']),
                                (1280, 720),
                                (1920, 1080),
                                (2560, 1440),
                                (3440, 1440),
                                (3840, 2160)
                            ]
                            # Remove duplicates and sort
                            common_resolutions = list(set(common_resolutions))
                            common_resolutions.sort(key=lambda x: x[0] * x[1])

                            if self.custom_resolution:
                                try:
                                    current_index = common_resolutions.index(self.custom_resolution)
                                    new_index = (current_index - 1) % len(common_resolutions)
                                    self.custom_resolution = common_resolutions[new_index]
                                except ValueError:
                                    self.custom_resolution = common_resolutions[0]
                            else:
                                self.custom_resolution = common_resolutions[-1]
                            print(f"Resolution changed to: {self.custom_resolution}")
                        elif next_res_rect.collidepoint(mouse_pos):
                            # Cycle through common resolutions (minimum 1280x720)
                            current_monitor = self.available_monitors[self.current_monitor]
                            common_resolutions = [
                                (current_monitor['width'], current_monitor['height']),
                                (1280, 720),
                                (1920, 1080),
                                (2560, 1440),
                                (3440, 1440),
                                (3840, 2160)
                            ]
                            # Remove duplicates and sort
                            common_resolutions = list(set(common_resolutions))
                            common_resolutions.sort(key=lambda x: x[0] * x[1])

                            if self.custom_resolution:
                                try:
                                    current_index = common_resolutions.index(self.custom_resolution)
                                    new_index = (current_index + 1) % len(common_resolutions)
                                    self.custom_resolution = common_resolutions[new_index]
                                except ValueError:
                                    self.custom_resolution = common_resolutions[1]
                            else:
                                self.custom_resolution = common_resolutions[1]
                            print(f"Resolution changed to: {self.custom_resolution}")

            # Draw input screen with solid background
            temp_screen.fill((40, 40, 40))  # Dark gray, not black
            
            # Title
            title_text = self.font.render("marine.EXE", True, self.GREEN)
            title_rect = title_text.get_rect(center=(300, 50))
            temp_screen.blit(title_text, title_rect)

            # Input prompt
            prompt_text = self.font.render("Enter your name:", True, self.WHITE)
            prompt_rect = prompt_text.get_rect(center=(300, 120))
            temp_screen.blit(prompt_text, prompt_rect)

            # Input box
            input_box = pygame.Rect(150, 150, 300, 32)
            pygame.draw.rect(temp_screen, self.WHITE, input_box, 2)

            # Input text
            text_surface = self.font.render(input_text, True, self.WHITE)
            temp_screen.blit(text_surface, (input_box.x + 5, input_box.y + 5))

            # Display Settings Section
            settings_y = 220
            settings_title = self.font.render("Display Settings:", True, self.WHITE)
            temp_screen.blit(settings_title, (50, settings_y))

            # Monitor setting
            monitor_y = settings_y + 40
            current_monitor = self.available_monitors[self.current_monitor]
            monitor_label = self.small_font.render("Monitor:", True, self.WHITE)
            temp_screen.blit(monitor_label, (50, monitor_y))

            # Monitor arrows and value
            prev_mon_rect = pygame.Rect(150, monitor_y, 30, 25)
            pygame.draw.rect(temp_screen, self.BLUE, prev_mon_rect)
            pygame.draw.rect(temp_screen, self.WHITE, prev_mon_rect, 2)
            prev_text = self.font.render("<", True, self.WHITE)
            temp_screen.blit(prev_text, (prev_mon_rect.x + 10, prev_mon_rect.y + 3))

            monitor_text = self.small_font.render(f"{self.current_monitor + 1}. {current_monitor['name'][:20]}", True, self.GREEN)
            temp_screen.blit(monitor_text, (190, monitor_y + 5))

            next_mon_rect = pygame.Rect(420, monitor_y, 30, 25)
            pygame.draw.rect(temp_screen, self.BLUE, next_mon_rect)
            pygame.draw.rect(temp_screen, self.WHITE, next_mon_rect, 2)
            next_text = self.font.render(">", True, self.WHITE)
            temp_screen.blit(next_text, (next_mon_rect.x + 10, next_mon_rect.y + 3))

            # Resolution setting
            res_y = monitor_y + 35
            res_label = self.small_font.render("Resolution:", True, self.WHITE)
            temp_screen.blit(res_label, (50, res_y))

            # Resolution arrows and value
            prev_res_rect = pygame.Rect(150, res_y, 30, 25)
            pygame.draw.rect(temp_screen, self.BLUE, prev_res_rect)
            pygame.draw.rect(temp_screen, self.WHITE, prev_res_rect, 2)
            prev_text = self.font.render("<", True, self.WHITE)
            temp_screen.blit(prev_text, (prev_res_rect.x + 10, prev_res_rect.y + 3))

            if self.custom_resolution:
                res_text = f"{self.custom_resolution[0]}x{self.custom_resolution[1]}"
            else:
                res_text = f"{current_monitor['width']}x{current_monitor['height']}"
            resolution_display = self.small_font.render(res_text, True, self.GREEN)
            temp_screen.blit(resolution_display, (190, res_y + 5))

            next_res_rect = pygame.Rect(420, res_y, 30, 25)
            pygame.draw.rect(temp_screen, self.BLUE, next_res_rect)
            pygame.draw.rect(temp_screen, self.WHITE, next_res_rect, 2)
            next_text = self.font.render(">", True, self.WHITE)
            temp_screen.blit(next_text, (next_res_rect.x + 10, next_res_rect.y + 3))

            # Instructions
            instr_text = self.font.render("Press ENTER to start", True, self.GRAY)
            temp_screen.blit(instr_text, (50, 360))

            pygame.display.flip()
            self.clock.tick(self.FPS)
    
    def initialize_game(self, player_name):
        """Initialize game components"""
        # Load map
        map_data = self.map_loader.load_map(0)
        self.game_state.current_map = map_data
        self.game_state.player_name = player_name
        
        # Create player
        player_x = self.SCREEN_WIDTH // 2
        player_y = self.SCREEN_HEIGHT // 2
        self.player = Player(player_x, player_y, self.image_handler, self.sound_manager)
        
        # Create monster manager
        self.monster_manager = MonsterManager(
            self.SCREEN_WIDTH, 
            self.SCREEN_HEIGHT, 
            map_data, 
            self.image_handler
        )
    
    def draw_info_panel(self):
        """Draw the information panel"""
        # Create a completely opaque surface for the panel
        panel_surface = pygame.Surface((self.info_panel_rect.width, self.info_panel_rect.height))
        panel_surface.fill((32, 32, 32))  # Dark gray background (not black to avoid transparency)

        # Draw border on the surface
        pygame.draw.rect(panel_surface, self.WHITE, (0, 0, self.info_panel_rect.width, self.info_panel_rect.height), 2)

        # Blit the solid panel to screen
        self.screen.blit(panel_surface, self.info_panel_rect.topleft)
        
        # Player info - draw relative to panel position
        panel_x = self.info_panel_rect.x
        panel_y = self.info_panel_rect.y
        y_offset = panel_y + 20

        # Player name
        name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
        self.screen.blit(name_text, (panel_x + 10, y_offset))
        y_offset += 25

        # HP
        hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
        self.screen.blit(hp_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Level
        level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
        self.screen.blit(level_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Map name
        map_name = self.game_state.current_map.get('map_name', 'Unknown')
        map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
        self.screen.blit(map_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Monster count
        monster_count = len(self.monster_manager.monsters)
        monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
        self.screen.blit(monster_text, (panel_x + 10, y_offset))

        # Options button
        button_x = panel_x + self.options_button_rect.x
        button_y = panel_y + self.options_button_rect.y
        button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)

        # Button background
        button_color = self.BLUE if not self.show_options else self.GRAY
        pygame.draw.rect(self.screen, button_color, button_rect)
        pygame.draw.rect(self.screen, self.WHITE, button_rect, 2)

        # Button text
        button_text = self.small_font.render("Options", True, self.WHITE)
        text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, text_rect)

        # Options panel (if shown)
        if self.show_options:
            # Expand panel height when options are shown (larger to fit all controls)
            expanded_panel_height = 400
            options_rect = pygame.Rect(panel_x, panel_y + 180, 400, 220)

            # Draw expanded panel background
            expanded_surface = pygame.Surface((400, expanded_panel_height))
            expanded_surface.fill((32, 32, 32))  # Dark gray, not black
            pygame.draw.rect(expanded_surface, self.WHITE, (0, 0, 400, expanded_panel_height), 2)
            self.screen.blit(expanded_surface, (panel_x, panel_y))

            # Redraw all panel content on expanded surface
            y_offset = 20
            name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
            self.screen.blit(name_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
            self.screen.blit(hp_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
            self.screen.blit(level_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            map_name = self.game_state.current_map.get('map_name', 'Unknown')
            map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
            self.screen.blit(map_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            monster_count = len(self.monster_manager.monsters)
            monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
            self.screen.blit(monster_text, (panel_x + 10, panel_y + y_offset))

            # Redraw options button
            button_x = panel_x + self.options_button_rect.x
            button_y = panel_y + self.options_button_rect.y
            button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)
            pygame.draw.rect(self.screen, self.GRAY, button_rect)
            pygame.draw.rect(self.screen, self.WHITE, button_rect, 2)
            button_text = self.small_font.render("Options", True, self.WHITE)
            text_rect = button_text.get_rect(center=button_rect.center)
            self.screen.blit(button_text, text_rect)

            # Options content
            pygame.draw.rect(self.screen, (48, 48, 48), options_rect)  # Darker gray for options area
            pygame.draw.rect(self.screen, self.WHITE, options_rect, 2)

            # Sound toggle
            sound_status = "ON" if self.sound_manager.is_sound_enabled() else "OFF"
            sound_color = self.GREEN if self.sound_manager.is_sound_enabled() else self.RED
            sound_text = self.small_font.render(f"Sound: {sound_status} (Click to toggle)", True, sound_color)
            self.screen.blit(sound_text, (options_rect.x + 10, options_rect.y + 10))

            # Volume control with +/- buttons
            volume_text = self.small_font.render(f"Volume: {int(self.sound_manager.volume * 100)}%", True, self.WHITE)
            self.screen.blit(volume_text, (options_rect.x + 10, options_rect.y + 30))

            # Volume - button
            vol_minus_rect = pygame.Rect(options_rect.x + 150, options_rect.y + 30, 20, 15)
            pygame.draw.rect(self.screen, self.RED, vol_minus_rect)
            pygame.draw.rect(self.screen, self.WHITE, vol_minus_rect, 1)
            minus_text = self.small_font.render("-", True, self.WHITE)
            self.screen.blit(minus_text, (vol_minus_rect.x + 7, vol_minus_rect.y + 1))

            # Volume + button
            vol_plus_rect = pygame.Rect(options_rect.x + 175, options_rect.y + 30, 20, 15)
            pygame.draw.rect(self.screen, self.GREEN, vol_plus_rect)
            pygame.draw.rect(self.screen, self.WHITE, vol_plus_rect, 1)
            plus_text = self.small_font.render("+", True, self.WHITE)
            self.screen.blit(plus_text, (vol_plus_rect.x + 6, vol_plus_rect.y + 1))

            # Monitor selection
            monitor_label = self.small_font.render("Monitor:", True, self.WHITE)
            self.screen.blit(monitor_label, (options_rect.x + 10, options_rect.y + 50))

            current_monitor = self.available_monitors[self.current_monitor]
            monitor_text = self.small_font.render(f"{self.current_monitor + 1}. {current_monitor['name'][:20]}", True, self.GREEN)
            self.screen.blit(monitor_text, (options_rect.x + 70, options_rect.y + 50))

            # Monitor change buttons
            prev_mon_rect = pygame.Rect(options_rect.x + 250, options_rect.y + 50, 20, 15)
            pygame.draw.rect(self.screen, self.BLUE, prev_mon_rect)
            pygame.draw.rect(self.screen, self.WHITE, prev_mon_rect, 1)
            prev_text = self.small_font.render("<", True, self.WHITE)
            self.screen.blit(prev_text, (prev_mon_rect.x + 7, prev_mon_rect.y + 1))

            next_mon_rect = pygame.Rect(options_rect.x + 275, options_rect.y + 50, 20, 15)
            pygame.draw.rect(self.screen, self.BLUE, next_mon_rect)
            pygame.draw.rect(self.screen, self.WHITE, next_mon_rect, 1)
            next_text = self.small_font.render(">", True, self.WHITE)
            self.screen.blit(next_text, (next_mon_rect.x + 7, next_mon_rect.y + 1))

            # Resolution with arrows
            res_label = self.small_font.render("Resolution:", True, self.WHITE)
            self.screen.blit(res_label, (options_rect.x + 10, options_rect.y + 75))

            # Resolution arrows
            prev_res_rect = pygame.Rect(options_rect.x + 80, options_rect.y + 75, 20, 15)
            pygame.draw.rect(self.screen, self.BLUE, prev_res_rect)
            pygame.draw.rect(self.screen, self.WHITE, prev_res_rect, 1)
            prev_res_text = self.small_font.render("<", True, self.WHITE)
            self.screen.blit(prev_res_text, (prev_res_rect.x + 7, prev_res_rect.y + 1))

            if self.custom_resolution:
                res_text = f"{self.custom_resolution[0]}x{self.custom_resolution[1]}"
                res_color = self.GREEN
            else:
                res_text = f"{current_monitor['width']}x{current_monitor['height']}"
                res_color = self.WHITE

            resolution_text = self.small_font.render(res_text, True, res_color)
            self.screen.blit(resolution_text, (options_rect.x + 105, options_rect.y + 75))

            next_res_rect = pygame.Rect(options_rect.x + 250, options_rect.y + 75, 20, 15)
            pygame.draw.rect(self.screen, self.BLUE, next_res_rect)
            pygame.draw.rect(self.screen, self.WHITE, next_res_rect, 1)
            next_res_text = self.small_font.render(">", True, self.WHITE)
            self.screen.blit(next_res_text, (next_res_rect.x + 7, next_res_rect.y + 1))

            # Custom resolution inputs
            width_label = self.small_font.render("W:", True, self.WHITE)
            self.screen.blit(width_label, (options_rect.x + 10, options_rect.y + 100))

            width_input_rect = pygame.Rect(options_rect.x + 30, options_rect.y + 100, 60, 15)
            width_color = self.GREEN if self.editing_width else self.WHITE
            pygame.draw.rect(self.screen, (32, 32, 32), width_input_rect)
            pygame.draw.rect(self.screen, width_color, width_input_rect, 1)
            width_text = self.small_font.render(self.custom_width_input, True, self.WHITE)
            self.screen.blit(width_text, (width_input_rect.x + 2, width_input_rect.y + 1))

            height_label = self.small_font.render("H:", True, self.WHITE)
            self.screen.blit(height_label, (options_rect.x + 100, options_rect.y + 100))

            height_input_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 100, 60, 15)
            height_color = self.GREEN if self.editing_height else self.WHITE
            pygame.draw.rect(self.screen, (32, 32, 32), height_input_rect)
            pygame.draw.rect(self.screen, height_color, height_input_rect, 1)
            height_text = self.small_font.render(self.custom_height_input, True, self.WHITE)
            self.screen.blit(height_text, (height_input_rect.x + 2, height_input_rect.y + 1))

            # Apply resolution button
            apply_res_rect = pygame.Rect(options_rect.x + 190, options_rect.y + 100, 50, 15)
            pygame.draw.rect(self.screen, self.BLUE, apply_res_rect)
            pygame.draw.rect(self.screen, self.WHITE, apply_res_rect, 1)
            apply_res_text = self.small_font.render("Apply", True, self.WHITE)
            self.screen.blit(apply_res_text, (apply_res_rect.x + 12, apply_res_rect.y + 1))

            # Zoom controls
            zoom_label = self.small_font.render("Zoom:", True, self.WHITE)
            self.screen.blit(zoom_label, (options_rect.x + 10, options_rect.y + 125))

            # Zoom arrows
            zoom_minus_rect = pygame.Rect(options_rect.x + 50, options_rect.y + 125, 20, 15)
            pygame.draw.rect(self.screen, self.RED, zoom_minus_rect)
            pygame.draw.rect(self.screen, self.WHITE, zoom_minus_rect, 1)
            zoom_minus_text = self.small_font.render("-", True, self.WHITE)
            self.screen.blit(zoom_minus_text, (zoom_minus_rect.x + 7, zoom_minus_rect.y + 1))

            zoom_text = f"{int(self.texture_zoom * 100)}%"
            zoom_display = self.small_font.render(zoom_text, True, self.GREEN)
            self.screen.blit(zoom_display, (options_rect.x + 75, options_rect.y + 125))

            zoom_plus_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 125, 20, 15)
            pygame.draw.rect(self.screen, self.GREEN, zoom_plus_rect)
            pygame.draw.rect(self.screen, self.WHITE, zoom_plus_rect, 1)
            zoom_plus_text = self.small_font.render("+", True, self.WHITE)
            self.screen.blit(zoom_plus_text, (zoom_plus_rect.x + 6, zoom_plus_rect.y + 1))

            # Save/Restart button
            save_btn_rect = pygame.Rect(options_rect.x + 10, options_rect.y + 150, 100, 25)
            pygame.draw.rect(self.screen, self.GREEN, save_btn_rect)
            pygame.draw.rect(self.screen, self.WHITE, save_btn_rect, 2)
            save_btn_text = self.small_font.render("Save & Restart", True, self.WHITE)
            self.screen.blit(save_btn_text, (save_btn_rect.x + 10, save_btn_rect.y + 5))

            # Instructions
            instr_text = self.small_font.render("Click outside to close options", True, self.GRAY)
            self.screen.blit(instr_text, (options_rect.x + 10, options_rect.y + 185))

    def restart_game(self):
        """Restart the game with new display settings"""
        print("Restarting game with new settings...")

        # Update display settings
        self.setup_display()

        # Recreate the screen
        pygame.display.quit()
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Setup transparent window again
        self.setup_transparent_window()

        # Reset UI state
        self.show_options = False

        # Reinitialize game objects with new screen size
        if self.player:
            # Keep player in bounds
            self.player.x = min(self.player.x, self.SCREEN_WIDTH - 50)
            self.player.y = min(self.player.y, self.SCREEN_HEIGHT - 50)
            self.player.x = max(50, self.player.x)
            self.player.y = max(50, self.player.y)

        print(f"Game restarted with resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")



    def run(self):
        """Main game loop"""
        # Get player name
        player_name = self.get_player_name()
        
        # Initialize game
        self.initialize_game(player_name)
        
        running = True
        while running:
            dt = self.clock.tick(self.FPS) / 1000.0  # Delta time in seconds
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif self.editing_width or self.editing_height:
                        # Handle custom resolution input
                        if event.key == pygame.K_BACKSPACE:
                            if self.editing_width:
                                self.custom_width_input = self.custom_width_input[:-1]
                            elif self.editing_height:
                                self.custom_height_input = self.custom_height_input[:-1]
                        elif event.key == pygame.K_RETURN:
                            self.editing_width = False
                            self.editing_height = False
                        elif event.unicode.isdigit() and len(event.unicode) == 1:
                            if self.editing_width and len(self.custom_width_input) < 5:
                                self.custom_width_input += event.unicode
                            elif self.editing_height and len(self.custom_height_input) < 5:
                                self.custom_height_input += event.unicode
                    elif self.show_options:
                        # Arrow keys for volume control
                        if event.key == pygame.K_LEFT:
                            new_volume = max(0.0, self.sound_manager.volume - 0.1)
                            self.sound_manager.set_volume(new_volume)
                        elif event.key == pygame.K_RIGHT:
                            new_volume = min(1.0, self.sound_manager.volume + 0.1)
                            self.sound_manager.set_volume(new_volume)
                        elif event.key == pygame.K_UP and self.show_options:
                            # Previous monitor
                            self.current_monitor = (self.current_monitor - 1) % len(self.available_monitors)
                            self.custom_resolution = None
                        elif event.key == pygame.K_DOWN and self.show_options:
                            # Next monitor
                            self.current_monitor = (self.current_monitor + 1) % len(self.available_monitors)
                            self.custom_resolution = None
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        mouse_pos = pygame.mouse.get_pos()
                        handled = False



                        # Check options button first
                        button_x = self.info_panel_rect.x + self.options_button_rect.x
                        button_y = self.info_panel_rect.y + self.options_button_rect.y
                        button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)

                        if button_rect.collidepoint(mouse_pos):
                            self.show_options = not self.show_options
                            handled = True
                        elif self.show_options:
                            # Check if clicking in options area (updated size)
                            options_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y + 180, 400, 220)
                            expanded_panel_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y, 400, 400)

                            if options_rect.collidepoint(mouse_pos):
                                # Check specific buttons in options
                                sound_area = pygame.Rect(options_rect.x + 10, options_rect.y + 10, 200, 20)
                                vol_minus_rect = pygame.Rect(options_rect.x + 150, options_rect.y + 30, 20, 15)
                                vol_plus_rect = pygame.Rect(options_rect.x + 175, options_rect.y + 30, 20, 15)

                                # Monitor buttons
                                prev_mon_rect = pygame.Rect(options_rect.x + 250, options_rect.y + 50, 20, 15)
                                next_mon_rect = pygame.Rect(options_rect.x + 275, options_rect.y + 50, 20, 15)

                                # Resolution arrows
                                prev_res_rect = pygame.Rect(options_rect.x + 80, options_rect.y + 75, 20, 15)
                                next_res_rect = pygame.Rect(options_rect.x + 250, options_rect.y + 75, 20, 15)

                                # Resolution inputs
                                width_input_rect = pygame.Rect(options_rect.x + 30, options_rect.y + 100, 60, 15)
                                height_input_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 100, 60, 15)
                                apply_res_rect = pygame.Rect(options_rect.x + 190, options_rect.y + 100, 50, 15)

                                # Zoom controls
                                zoom_minus_rect = pygame.Rect(options_rect.x + 50, options_rect.y + 125, 20, 15)
                                zoom_plus_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 125, 20, 15)

                                # Save button
                                save_btn_rect = pygame.Rect(options_rect.x + 10, options_rect.y + 150, 100, 25)

                                if sound_area.collidepoint(mouse_pos):
                                    self.sound_manager.toggle_sound()
                                elif vol_minus_rect.collidepoint(mouse_pos):
                                    new_volume = max(0.0, self.sound_manager.volume - 0.1)
                                    self.sound_manager.set_volume(new_volume)
                                elif vol_plus_rect.collidepoint(mouse_pos):
                                    new_volume = min(1.0, self.sound_manager.volume + 0.1)
                                    self.sound_manager.set_volume(new_volume)
                                elif prev_mon_rect.collidepoint(mouse_pos):
                                    self.current_monitor = (self.current_monitor - 1) % len(self.available_monitors)
                                    self.custom_resolution = None
                                elif next_mon_rect.collidepoint(mouse_pos):
                                    self.current_monitor = (self.current_monitor + 1) % len(self.available_monitors)
                                    self.custom_resolution = None
                                elif prev_res_rect.collidepoint(mouse_pos):
                                    # Cycle through resolutions (minimum 1280x720)
                                    current_monitor = self.available_monitors[self.current_monitor]
                                    common_resolutions = [
                                        (current_monitor['width'], current_monitor['height']),
                                        (1280, 720),
                                        (1920, 1080),
                                        (2560, 1440),
                                        (3440, 1440),
                                        (3840, 2160)
                                    ]
                                    # Remove duplicates and sort
                                    common_resolutions = list(set(common_resolutions))
                                    common_resolutions.sort(key=lambda x: x[0] * x[1])

                                    if self.custom_resolution:
                                        try:
                                            current_index = common_resolutions.index(self.custom_resolution)
                                            new_index = (current_index - 1) % len(common_resolutions)
                                            self.custom_resolution = common_resolutions[new_index]
                                        except ValueError:
                                            self.custom_resolution = common_resolutions[0]
                                    else:
                                        self.custom_resolution = common_resolutions[-1]
                                elif next_res_rect.collidepoint(mouse_pos):
                                    # Cycle through resolutions (minimum 1280x720)
                                    current_monitor = self.available_monitors[self.current_monitor]
                                    common_resolutions = [
                                        (current_monitor['width'], current_monitor['height']),
                                        (1280, 720),
                                        (1920, 1080),
                                        (2560, 1440),
                                        (3440, 1440),
                                        (3840, 2160)
                                    ]
                                    # Remove duplicates and sort
                                    common_resolutions = list(set(common_resolutions))
                                    common_resolutions.sort(key=lambda x: x[0] * x[1])

                                    if self.custom_resolution:
                                        try:
                                            current_index = common_resolutions.index(self.custom_resolution)
                                            new_index = (current_index + 1) % len(common_resolutions)
                                            self.custom_resolution = common_resolutions[new_index]
                                        except ValueError:
                                            self.custom_resolution = common_resolutions[1]
                                    else:
                                        self.custom_resolution = common_resolutions[1]
                                elif zoom_minus_rect.collidepoint(mouse_pos):
                                    # Decrease zoom
                                    self.texture_zoom = max(self.min_zoom, self.texture_zoom - self.zoom_step)
                                    self.image_handler.set_texture_zoom(self.texture_zoom)
                                    print(f"Zoom decreased to {int(self.texture_zoom * 100)}%")
                                elif zoom_plus_rect.collidepoint(mouse_pos):
                                    # Increase zoom
                                    self.texture_zoom = min(self.max_zoom, self.texture_zoom + self.zoom_step)
                                    self.image_handler.set_texture_zoom(self.texture_zoom)
                                    print(f"Zoom increased to {int(self.texture_zoom * 100)}%")
                                elif width_input_rect.collidepoint(mouse_pos):
                                    self.editing_width = True
                                    self.editing_height = False
                                elif height_input_rect.collidepoint(mouse_pos):
                                    self.editing_height = True
                                    self.editing_width = False
                                elif apply_res_rect.collidepoint(mouse_pos):
                                    # Apply custom resolution
                                    try:
                                        width = int(self.custom_width_input) if self.custom_width_input else 0
                                        height = int(self.custom_height_input) if self.custom_height_input else 0
                                        if width > 0 and height > 0:
                                            self.custom_resolution = (width, height)
                                            print(f"Custom resolution set: {width}x{height}")
                                    except ValueError:
                                        print("Invalid resolution values")
                                elif save_btn_rect.collidepoint(mouse_pos):
                                    self.restart_game()
                                else:
                                    self.editing_width = False
                                    self.editing_height = False
                                handled = True
                            elif not expanded_panel_rect.collidepoint(mouse_pos):
                                self.show_options = False
                                self.editing_width = False
                                self.editing_height = False

                        if not handled:
                            # Check if clicking on panel (consider expanded size if options are shown)
                            panel_rect = self.info_panel_rect
                            if self.show_options:
                                panel_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y, 400, 400)

                            if panel_rect.collidepoint(mouse_pos):
                                # Start dragging panel
                                self.panel_dragging = True
                                self.panel_drag_offset_x = mouse_pos[0] - self.info_panel_rect.x
                                self.panel_drag_offset_y = mouse_pos[1] - self.info_panel_rect.y
                                print(f"Panel drag started at {mouse_pos}")
                            else:
                                # Try to drag player - always try if nothing else was clicked
                                if self.player.start_drag(mouse_pos):
                                    print("Player drag started")
                                else:
                                    print(f"No drag target found at {mouse_pos}")
                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # Left mouse button
                        self.panel_dragging = False
                        self.player.stop_drag()
                elif event.type == pygame.MOUSEMOTION:
                    mouse_pos = pygame.mouse.get_pos()
                    if self.panel_dragging:
                        # Move panel
                        new_x = mouse_pos[0] - self.panel_drag_offset_x
                        new_y = mouse_pos[1] - self.panel_drag_offset_y
                        self.info_panel_rect.x = new_x
                        self.info_panel_rect.y = new_y
                        print(f"Panel moved to {new_x}, {new_y}")
                    elif self.player.dragging:
                        self.player.update_drag(mouse_pos)
            
            # Store previous HP values for damage detection
            prev_player_hp = self.player.hp
            prev_monster_hp = {id(m): m.hp for m in self.monster_manager.monsters}

            # Update game objects
            self.player.update(dt, self.monster_manager.monsters)
            self.monster_manager.update(dt, self.player)
            self.damage_display.update(dt)

            # Check for damage and add damage text
            # Player damage
            if self.player.hp < prev_player_hp:
                damage = prev_player_hp - self.player.hp
                self.damage_display.add_damage_text(self.player.x, self.player.y - 20, damage, is_player=True)

            # Monster damage
            for monster in self.monster_manager.monsters:
                monster_id = id(monster)
                if monster_id in prev_monster_hp and monster.hp < prev_monster_hp[monster_id]:
                    damage = prev_monster_hp[monster_id] - monster.hp
                    self.damage_display.add_damage_text(monster.x, monster.y - 20, damage, is_player=False)
            
            # Clear screen with transparent black
            self.screen.fill((0, 0, 0))
            
            # Draw game objects
            self.player.draw(self.screen)
            self.monster_manager.draw(self.screen)

            # Draw damage numbers
            self.damage_display.draw(self.screen)

            # Draw UI
            self.draw_info_panel()



            # Update display
            pygame.display.flip()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = MarineEXE()
    game.run()
